/**
 * Convert base64 string to data URL
 * @param base64 - Base64 encoded string (with or without data URL prefix)
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns Data URL string
 * @example
 * ```ts
 * const dataUrl = base64ToDataUrl('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', 'image/png');
 * // Returns: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='
 * ```
 */
export function base64ToDataUrl(base64: string, mimeType: string = 'image/png'): string {
  if (!base64) {
    throw new Error('Base64 string is required');
  }

  // Remove data URL prefix if it exists
  const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

  // Validate base64 format
  if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
    throw new Error('Invalid base64 format');
  }

  return `data:${mimeType};base64,${cleanBase64}`;
}

/**
 * Convert base64 string to blob URL (more memory efficient for large files)
 * @param base64 - Base64 encoded string (with or without data URL prefix)
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns Blob URL string
 * @example
 * ```ts
 * const blobUrl = base64ToBlobUrl('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', 'image/png');
 * // Returns: 'blob:http://localhost:3000/550e8400-e29b-41d4-a716-446655440000'
 * // Remember to call revokeBlobUrl(blobUrl) when done
 * ```
 */
export function base64ToBlobUrl(base64: string, mimeType: string = 'image/png'): string {
  if (!base64) {
    throw new Error('Base64 string is required');
  }

  try {
    // Remove data URL prefix if it exists
    const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

    // Convert base64 to binary using more efficient method
    const byteCharacters = atob(cleanBase64);
    const byteArray = new Uint8Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i += 1) {
      byteArray[i] = byteCharacters.charCodeAt(i);
    }

    const blob = new Blob([byteArray], { type: mimeType });
    return URL.createObjectURL(blob);
  } catch (error) {
    throw new Error(
      `Failed to convert base64 to blob URL: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Revoke blob URL to free memory
 * @param blobUrl - Blob URL to revoke
 * @example
 * ```ts
 * const blobUrl = base64ToBlobUrl(base64String);
 * // Use the blob URL...
 * revokeBlobUrl(blobUrl); // Clean up when done
 * ```
 */
export function revokeBlobUrl(blobUrl: string): void {
  if (!blobUrl) {
    return;
  }

  if (!blobUrl.startsWith('blob:')) {
    // eslint-disable-next-line no-console
    console.warn('Provided URL is not a blob URL:', blobUrl);
    return;
  }

  URL.revokeObjectURL(blobUrl);
}

/**
 * Convert base64 to File object
 * @param base64 - Base64 encoded string (with or without data URL prefix)
 * @param filename - Name for the file
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns File object
 * @example
 * ```ts
 * const file = base64ToFile(base64String, 'image.png', 'image/png');
 * // Can be used with FormData or file upload
 * ```
 */
export function base64ToFile(base64: string, filename: string, mimeType: string = 'image/png'): File {
  if (!base64) {
    throw new Error('Base64 string is required');
  }

  if (!filename) {
    throw new Error('Filename is required');
  }

  try {
    // Remove data URL prefix if it exists
    const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

    // Convert base64 to binary
    const byteCharacters = atob(cleanBase64);
    const byteArray = new Uint8Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i += 1) {
      byteArray[i] = byteCharacters.charCodeAt(i);
    }

    return new File([byteArray], filename, { type: mimeType });
  } catch (error) {
    throw new Error(`Failed to convert base64 to file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Auto-detect MIME type from base64 data URL
 * @param base64 - Base64 string with data URL prefix
 * @returns Detected MIME type or null if not found
 * @example
 * ```ts
 * const mimeType = detectMimeType('data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...');
 * // Returns: 'image/jpeg'
 * ```
 */
export function detectMimeType(base64: string): string | null {
  const match = base64.match(/^data:([^;]+);base64,/);
  return match ? match[1] : null;
}
