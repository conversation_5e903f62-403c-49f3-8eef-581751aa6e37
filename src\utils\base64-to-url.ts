/**
 * Convert base64 string to data URL
 * @param base64 - Base64 encoded string
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns Data URL string
 */
export function base64ToDataUrl(base64: string, mimeType: string = 'image/png'): string {
  // Remove data URL prefix if it exists
  const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

  return `data:${mimeType};base64,${cleanBase64}`;
}

/**
 * Convert base64 string to blob URL
 * @param base64 - Base64 encoded string
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns Blob URL string
 */
export function base64ToBlobUrl(base64: string, mimeType: string = 'image/png'): string {
  // Remove data URL prefix if it exists
  const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

  // Convert base64 to binary
  const byteCharacters = atob(cleanBase64);
  const byteNumbers = Array.from({ length: byteCharacters.length });

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: mimeType });

  return URL.createObjectURL(blob);
}

/**
 * Revoke blob URL to free memory
 * @param blobUrl - Blob URL to revoke
 */
export function revokeBlobUrl(blobUrl: string): void {
  URL.revokeObjectURL(blobUrl);
}
