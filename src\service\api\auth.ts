import { request } from '../request';

/**
 * Convert base64 string to data URL
 * @param base64 - Base64 encoded string
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns Data URL string
 */
export function base64ToDataUrl(base64: string, mimeType: string = 'image/png'): string {
  // Remove data URL prefix if it exists
  const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

  return `data:${mimeType};base64,${cleanBase64}`;
}

/**
 * Convert base64 string to blob URL
 * @param base64 - Base64 encoded string
 * @param mimeType - MIME type of the data (default: 'image/png')
 * @returns Blob URL string
 */
export function base64ToBlobUrl(base64: string, mimeType: string = 'image/png'): string {
  // Remove data URL prefix if it exists
  const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, '');

  // Convert base64 to binary
  const byteCharacters = atob(cleanBase64);
  const byteNumbers = Array.from({ length: byteCharacters.length });

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: mimeType });

  return URL.createObjectURL(blob);
}

/**
 * Revoke blob URL to free memory
 * @param blobUrl - Blob URL to revoke
 */
export function revokeBlobUrl(blobUrl: string): void {
  URL.revokeObjectURL(blobUrl);
}
/** Get tenant list */
export function fetchTenantList() {
  return request<Api.Auth.LoginTenant>({
    url: '/auth/tenant/list',
    method: 'get'
  });
}

/** Get image code */
export function fetchCaptchaCode() {
  return request<Api.Auth.CaptchaCode>({
    url: '/api/system/auth/captcha',
    method: 'get',
    headers: {
      isToken: false
    }
  });
}

/**
 * Login
 *
 * @param username User name
 * @param password Password
 */
export function fetchLogin(data: Api.Auth.PwdLoginForm) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/login',
    method: 'post',
    headers: {
      isToken: false,
      isEncrypt: true,
      repeatSubmit: false
    },
    data
  });
}

/** social login callback */
export function fetchSocialLoginCallback(data: Api.Auth.SocialLoginForm) {
  return request({
    url: '/auth/social/callback',
    method: 'post',
    data
  });
}

/** Register */
export function fetchRegister(data: Api.Auth.RegisterForm) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/register',
    method: 'post',
    headers: {
      isToken: false,
      isEncrypt: true,
      repeatSubmit: false
    },
    data
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/system/user/getInfo' });
}

/** Logout */
export function fetchLogout() {
  if (import.meta.env.VITE_APP_SSE === 'Y') {
    request({
      url: '/resource/sse/close',
      method: 'get'
    });
  }
  return request({
    url: '/auth/logout',
    method: 'post'
  });
}
